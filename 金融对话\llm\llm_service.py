"""
在线大模型调用服务
支持多种在线大模型服务
"""
import openai
import requests
import json
import hashlib
import hmac
import base64
import time
import urllib.parse
import pickle
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Dict, Any, List, Optional, Tuple
from loguru import logger
import sys
from pathlib import Path

# 添加父目录到Python路径
parent_dir = Path(__file__).parent.parent
sys.path.insert(0, str(parent_dir))

from idconfig.config import Config

try:
    import anthropic
    ANTHROPIC_AVAILABLE = True
except ImportError:
    ANTHROPIC_AVAILABLE = False
    logger.warning("anthropic库未安装，Claude模型不可用")

try:
    import google.generativeai as genai
    GOOGLE_AVAILABLE = True
except ImportError:
    GOOGLE_AVAILABLE = False
    logger.warning("google-generativeai库未安装，Gemini模型不可用")

try:
    from tencentcloud.common import credential
    from tencentcloud.common.profile.client_profile import ClientProfile
    from tencentcloud.common.profile.http_profile import HttpProfile
    from tencentcloud.hunyuan.v20230901 import hunyuan_client, models
    TENCENT_AVAILABLE = True
except ImportError:
    TENCENT_AVAILABLE = False
    logger.warning("tencentcloud-sdk-python库未安装，腾讯混元模型不可用")

class LLMService:
    def __init__(self):
        self.config = Config()
        self.setup_openai()
    
    def setup_openai(self):
        """设置OpenAI客户端"""
        if self.config.OPENAI_API_KEY:
            openai.api_key = self.config.OPENAI_API_KEY
            if self.config.OPENAI_BASE_URL:
                openai.base_url = self.config.OPENAI_BASE_URL
            logger.info("OpenAI客户端配置完成")
    
    def call_openai(self, messages: List[Dict[str, str]], temperature: float = 0.7) -> str:
        """调用OpenAI API"""
        try:
            if not self.config.OPENAI_API_KEY:
                logger.error("OpenAI API密钥未配置")
                return "抱歉，大模型服务暂时不可用。"
            
            client = openai.OpenAI(
                api_key=self.config.OPENAI_API_KEY,
                base_url=self.config.OPENAI_BASE_URL
            )
            
            response = client.chat.completions.create(
                model=self.config.MODEL_NAME,
                messages=messages,
                temperature=temperature,
                max_tokens=2000
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logger.error(f"调用OpenAI API失败: {e}")
            return "抱歉，处理您的请求时出现了错误。"
    
    def call_zhipu(self, messages: List[Dict[str, str]], temperature: float = 0.7) -> str:
        """调用智谱AI API"""
        try:
            if not self.config.ZHIPU_API_KEY:
                logger.error("智谱AI API密钥未配置")
                return "抱歉，智谱AI服务暂时不可用。"
            
            url = "https://open.bigmodel.cn/api/paas/v4/chat/completions"
            headers = {
                "Authorization": f"Bearer {self.config.ZHIPU_API_KEY}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": self.config.ZHIPU_MODEL,
                "messages": messages,
                "temperature": temperature,
                "max_tokens": 2000
            }
            
            response = requests.post(url, headers=headers, json=data, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            return result["choices"][0]["message"]["content"]
            
        except Exception as e:
            logger.error(f"调用智谱AI API失败: {e}")
            return "抱歉，处理您的请求时出现了错误。"
    
    def call_qwen(self, messages: List[Dict[str, str]], temperature: float = 0.7) -> str:
        """调用通义千问API"""
        try:
            if not self.config.QWEN_API_KEY:
                logger.error("通义千问API密钥未配置")
                return "抱歉，通义千问服务暂时不可用。"

            # 这里需要根据实际的通义千问API接口进行调整
            url = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"
            headers = {
                "Authorization": f"Bearer {self.config.QWEN_API_KEY}",
                "Content-Type": "application/json"
            }

            # 将messages转换为通义千问格式
            prompt = self._convert_messages_to_prompt(messages)

            data = {
                "model": self.config.QWEN_MODEL,
                "input": {
                    "prompt": prompt
                },
                "parameters": {
                    "temperature": temperature,
                    "max_tokens": 2000
                }
            }

            response = requests.post(url, headers=headers, json=data, timeout=30)
            response.raise_for_status()

            result = response.json()
            return result["output"]["text"]

        except Exception as e:
            logger.error(f"调用通义千问API失败: {e}")
            return "抱歉，处理您的请求时出现了错误。"

    def call_siliconflow(self, messages: List[Dict[str, str]], temperature: float = 0.7, model: str = None) -> str:
        """调用硅基流动API"""
        try:
            if not self.config.SILICONFLOW_API_KEY:
                logger.error("硅基流动API密钥未配置")
                return "抱歉，硅基流动服务暂时不可用。"

            url = "https://api.siliconflow.cn/v1/chat/completions"
            headers = {
                "Authorization": f"Bearer {self.config.SILICONFLOW_API_KEY}",
                "Content-Type": "application/json"
            }

            data = {
                "model": model or self.config.SILICONFLOW_MODEL,
                "messages": messages,
                "temperature": temperature,
                "max_tokens": 2000,
                "stream": False
            }

            response = requests.post(url, headers=headers, json=data, timeout=60)
            response.raise_for_status()

            result = response.json()
            return result["choices"][0]["message"]["content"]

        except Exception as e:
            logger.error(f"调用硅基流动API失败: {e}")
            return "抱歉，处理您的请求时出现了错误。"

    def call_siliconflow2(self, messages: List[Dict[str, str]], temperature: float = 0.7, model: str = None) -> str:
        """调用硅基流动2 API (额外的硅基流动配置)"""
        try:
            if not self.config.SILICONFLOW2_API_KEY:
                logger.error("硅基流动2 API密钥未配置")
                return "抱歉，硅基流动2服务暂时不可用。"

            url = f"{self.config.SILICONFLOW2_BASE_URL}/chat/completions"
            headers = {
                "Authorization": f"Bearer {self.config.SILICONFLOW2_API_KEY}",
                "Content-Type": "application/json"
            }

            data = {
                "model": model or self.config.SILICONFLOW2_MODEL,
                "messages": messages,
                "temperature": temperature,
                "max_tokens": 2000,
                "stream": False
            }

            response = requests.post(url, headers=headers, json=data, timeout=60)
            response.raise_for_status()

            result = response.json()
            return result["choices"][0]["message"]["content"]

        except Exception as e:
            logger.error(f"调用硅基流动2 API失败: {e}")
            return "抱歉，处理您的请求时出现了错误。"

    def call_siliconflow2_vision(self, image_base64: str, prompt: str = "请详细描述这张图像的内容", temperature: float = 0.7, model: str = None) -> str:
        """调用硅基流动2的多模态视觉API分析图像"""
        try:
            if not self.config.SILICONFLOW2_API_KEY:
                logger.error("硅基流动2 API密钥未配置")
                return "抱歉，硅基流动2服务暂时不可用。"

            url = f"{self.config.SILICONFLOW2_BASE_URL}/chat/completions"
            headers = {
                "Authorization": f"Bearer {self.config.SILICONFLOW2_API_KEY}",
                "Content-Type": "application/json"
            }

            # 构建多模态消息
            messages = [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": prompt
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{image_base64}"
                            }
                        }
                    ]
                }
            ]

            data = {
                "model": model or self.config.SILICONFLOW2_MODEL,
                "messages": messages,
                "temperature": temperature,
                "max_tokens": 2000,
                "stream": False
            }

            response = requests.post(url, headers=headers, json=data, timeout=60)
            response.raise_for_status()

            result = response.json()
            return result["choices"][0]["message"]["content"]

        except Exception as e:
            logger.error(f"调用硅基流动2视觉API失败: {e}")
            return "抱歉，图像分析时出现了错误。"

    def batch_call_siliconflow2_vision(self, image_data_list: List[Dict[str, Any]],
                                     base_prompt: str = "请详细描述这张图像的内容",
                                     temperature: float = 0.7,
                                     model: str = None,
                                     max_workers: int = 3) -> List[Dict[str, Any]]:
        """批量调用硅基流动2多模态API生成图像描述

        Args:
            image_data_list: 图像数据列表，每个元素包含:
                - image_base64: base64编码的图像
                - image_id: 图像唯一标识
                - context_info: 上下文信息（可选）
                - custom_prompt: 自定义提示词（可选）
            base_prompt: 基础提示词
            temperature: 温度参数
            model: 模型名称
            max_workers: 最大并发数

        Returns:
            List[Dict]: 包含图像描述结果的列表
        """
        try:
            if not self.config.SILICONFLOW2_API_KEY:
                logger.error("硅基流动2 API密钥未配置")
                return [{"image_id": item.get("image_id", "unknown"),
                        "description": "抱歉，硅基流动2服务暂时不可用。",
                        "success": False} for item in image_data_list]

            results = []

            def process_single_image(image_data: Dict[str, Any]) -> Dict[str, Any]:
                """处理单个图像"""
                image_id = image_data.get("image_id", "unknown")
                image_base64 = image_data.get("image_base64", "")
                context_info = image_data.get("context_info", {})
                custom_prompt = image_data.get("custom_prompt", "")

                try:
                    # 构建增强的提示词
                    enhanced_prompt = self._build_enhanced_prompt(
                        base_prompt, custom_prompt, context_info
                    )

                    # 调用API
                    description = self.call_siliconflow2_vision(
                        image_base64=image_base64,
                        prompt=enhanced_prompt,
                        temperature=temperature,
                        model=model
                    )

                    return {
                        "image_id": image_id,
                        "description": description,
                        "success": True,
                        "prompt_used": enhanced_prompt,
                        "context_info": context_info
                    }

                except Exception as e:
                    logger.error(f"处理图像 {image_id} 时出错: {e}")
                    return {
                        "image_id": image_id,
                        "description": f"处理图像时出现错误: {str(e)}",
                        "success": False,
                        "error": str(e)
                    }

            # 使用线程池并发处理
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # 提交所有任务
                future_to_image = {
                    executor.submit(process_single_image, image_data): image_data
                    for image_data in image_data_list
                }

                # 收集结果
                for future in as_completed(future_to_image):
                    try:
                        result = future.result()
                        results.append(result)
                        logger.info(f"成功处理图像: {result['image_id']}")
                    except Exception as e:
                        image_data = future_to_image[future]
                        image_id = image_data.get("image_id", "unknown")
                        logger.error(f"处理图像 {image_id} 的future时出错: {e}")
                        results.append({
                            "image_id": image_id,
                            "description": f"处理图像时出现错误: {str(e)}",
                            "success": False,
                            "error": str(e)
                        })

            # 按原始顺序排序结果
            image_id_to_index = {item.get("image_id"): i for i, item in enumerate(image_data_list)}
            results.sort(key=lambda x: image_id_to_index.get(x["image_id"], float('inf')))

            logger.info(f"批量处理完成，共处理 {len(results)} 张图像")
            return results

        except Exception as e:
            logger.error(f"批量处理图像时出错: {e}")
            return [{"image_id": item.get("image_id", "unknown"),
                    "description": f"批量处理时出现错误: {str(e)}",
                    "success": False,
                    "error": str(e)} for item in image_data_list]

    def _build_enhanced_prompt(self, base_prompt: str, custom_prompt: str = "", context_info: Dict[str, Any] = None) -> str:
        """构建增强的提示词，结合上下文信息"""
        if context_info is None:
            context_info = {}

        # 开始构建提示词
        enhanced_parts = []

        # 添加基础提示词
        if base_prompt:
            enhanced_parts.append(base_prompt)

        # 添加上下文信息
        if context_info:
            context_parts = []

            # 文档信息
            if context_info.get("pdf_name"):
                context_parts.append(f"文档来源: {context_info['pdf_name']}")

            if context_info.get("page_number"):
                context_parts.append(f"页码: 第{context_info['page_number']}页")

            # 图像类型信息
            if context_info.get("image_type"):
                context_parts.append(f"图像类型: {context_info['image_type']}")

            # 周围文本信息
            if context_info.get("surrounding_text"):
                context_parts.append(f"周围文本: {context_info['surrounding_text']}")

            # 表格信息
            if context_info.get("table_info"):
                context_parts.append(f"相关表格: {context_info['table_info']}")

            # 财务指标信息
            if context_info.get("financial_metrics"):
                context_parts.append(f"相关财务指标: {context_info['financial_metrics']}")

            if context_parts:
                enhanced_parts.append("上下文信息：" + "；".join(context_parts))

        # 添加自定义提示词
        if custom_prompt:
            enhanced_parts.append(custom_prompt)

        # 添加专业分析要求
        analysis_requirements = [
            "请特别关注以下方面：",
            "1. 图表类型和数据结构",
            "2. 关键数据点和趋势",
            "3. 财务指标的含义和变化",
            "4. 时间序列数据的分析",
            "5. 图像中的文字和数字信息",
            "6. 专业术语和概念的解释"
        ]
        enhanced_parts.extend(analysis_requirements)

        return "\n\n".join(enhanced_parts)

    def batch_generate_contextual_descriptions(self, images_with_context: List[Dict[str, Any]],
                                             temperature: float = 0.7,
                                             model: str = None,
                                             max_workers: int = 3) -> List[Dict[str, Any]]:
        """批量生成带上下文增强的图像描述

        这是一个高级接口，专门用于金融文档图像的上下文增强描述生成

        Args:
            images_with_context: 包含图像和上下文信息的列表，每个元素包含:
                - image_base64: base64编码的图像
                - image_id: 图像唯一标识
                - pdf_name: PDF文档名称
                - page_number: 页码
                - image_type: 图像类型（如：图表、表格、流程图等）
                - surrounding_text: 周围文本内容
                - table_info: 相关表格信息
                - financial_metrics: 相关财务指标
                - query_context: 查询上下文（用户问题相关）
            temperature: 温度参数
            model: 模型名称
            max_workers: 最大并发数

        Returns:
            List[Dict]: 包含详细描述结果的列表
        """
        try:
            # 为每个图像构建专门的提示词
            enhanced_image_data = []

            for item in images_with_context:
                # 基础提示词
                base_prompt = "作为一名专业的金融分析师，请详细分析这张图像。"

                # 根据图像类型调整提示词
                image_type = item.get("image_type", "").lower()
                if "图表" in image_type or "chart" in image_type:
                    base_prompt += "这是一张图表，请重点分析数据趋势、关键指标和变化规律。"
                elif "表格" in image_type or "table" in image_type:
                    base_prompt += "这是一张表格，请详细解读表格结构、数据内容和财务含义。"
                elif "流程图" in image_type or "流程" in image_type:
                    base_prompt += "这是一张流程图，请分析业务流程、关键节点和逻辑关系。"
                else:
                    base_prompt += "请全面分析图像内容，特别关注财务相关信息。"

                # 构建上下文信息
                context_info = {
                    "pdf_name": item.get("pdf_name", ""),
                    "page_number": item.get("page_number", ""),
                    "image_type": item.get("image_type", ""),
                    "surrounding_text": item.get("surrounding_text", ""),
                    "table_info": item.get("table_info", ""),
                    "financial_metrics": item.get("financial_metrics", "")
                }

                # 添加查询相关的自定义提示词
                custom_prompt = ""
                if item.get("query_context"):
                    custom_prompt = f"特别关注与以下查询相关的内容：{item['query_context']}"

                enhanced_item = {
                    "image_id": item.get("image_id", ""),
                    "image_base64": item.get("image_base64", ""),
                    "context_info": context_info,
                    "custom_prompt": custom_prompt
                }
                enhanced_image_data.append(enhanced_item)

            # 调用批量处理方法
            results = self.batch_call_siliconflow2_vision(
                image_data_list=enhanced_image_data,
                base_prompt=base_prompt,
                temperature=temperature,
                model=model,
                max_workers=max_workers
            )

            # 增强结果信息
            for i, result in enumerate(results):
                if i < len(images_with_context):
                    original_item = images_with_context[i]
                    result.update({
                        "pdf_name": original_item.get("pdf_name", ""),
                        "page_number": original_item.get("page_number", ""),
                        "image_type": original_item.get("image_type", ""),
                        "processing_time": time.time()  # 添加处理时间戳
                    })

            return results

        except Exception as e:
            logger.error(f"批量生成上下文增强描述时出错: {e}")
            return [{"image_id": item.get("image_id", "unknown"),
                    "description": f"生成描述时出现错误: {str(e)}",
                    "success": False,
                    "error": str(e)} for item in images_with_context]

    def call_baidu(self, messages: List[Dict[str, str]], temperature: float = 0.7) -> str:
        """调用百度文心一言API"""
        try:
            if not self.config.BAIDU_API_KEY or not self.config.BAIDU_SECRET_KEY:
                logger.error("百度API密钥未配置")
                return "抱歉，百度文心一言服务暂时不可用。"

            # 获取access_token
            access_token = self._get_baidu_access_token()
            if not access_token:
                return "抱歉，获取百度访问令牌失败。"

            url = f"{self.config.BAIDU_BASE_URL}/chat/completions_pro?access_token={access_token}"
            headers = {
                "Content-Type": "application/json"
            }

            data = {
                "messages": messages,
                "temperature": temperature,
                "max_output_tokens": 2000
            }

            response = requests.post(url, headers=headers, json=data, timeout=30)
            response.raise_for_status()

            result = response.json()
            if "result" in result:
                return result["result"]
            else:
                logger.error(f"百度API返回格式错误: {result}")
                return "抱歉，处理您的请求时出现了错误。"

        except Exception as e:
            logger.error(f"调用百度文心一言API失败: {e}")
            return "抱歉，处理您的请求时出现了错误。"

    def _get_baidu_access_token(self) -> str:
        """获取百度access_token"""
        try:
            url = "https://aip.baidubce.com/oauth/2.0/token"
            params = {
                "grant_type": "client_credentials",
                "client_id": self.config.BAIDU_API_KEY,
                "client_secret": self.config.BAIDU_SECRET_KEY
            }

            response = requests.post(url, params=params, timeout=10)
            response.raise_for_status()

            result = response.json()
            return result.get("access_token", "")

        except Exception as e:
            logger.error(f"获取百度access_token失败: {e}")
            return ""

    def call_moonshot(self, messages: List[Dict[str, str]], temperature: float = 0.7) -> str:
        """调用Moonshot AI (Kimi) API"""
        try:
            if not self.config.MOONSHOT_API_KEY:
                logger.error("Moonshot API密钥未配置")
                return "抱歉，Moonshot AI服务暂时不可用。"

            url = f"{self.config.MOONSHOT_BASE_URL}/chat/completions"
            headers = {
                "Authorization": f"Bearer {self.config.MOONSHOT_API_KEY}",
                "Content-Type": "application/json"
            }

            data = {
                "model": self.config.MOONSHOT_MODEL,
                "messages": messages,
                "temperature": temperature,
                "max_tokens": 2000
            }

            response = requests.post(url, headers=headers, json=data, timeout=30)
            response.raise_for_status()

            result = response.json()
            return result["choices"][0]["message"]["content"]

        except Exception as e:
            logger.error(f"调用Moonshot AI API失败: {e}")
            return "抱歉，处理您的请求时出现了错误。"

    def call_deepseek(self, messages: List[Dict[str, str]], temperature: float = 0.7) -> str:
        """调用DeepSeek API"""
        try:
            if not self.config.DEEPSEEK_API_KEY:
                logger.error("DeepSeek API密钥未配置")
                return "抱歉，DeepSeek服务暂时不可用。"

            url = f"{self.config.DEEPSEEK_BASE_URL}/chat/completions"
            headers = {
                "Authorization": f"Bearer {self.config.DEEPSEEK_API_KEY}",
                "Content-Type": "application/json"
            }

            data = {
                "model": self.config.DEEPSEEK_MODEL,
                "messages": messages,
                "temperature": temperature,
                "max_tokens": 2000
            }

            response = requests.post(url, headers=headers, json=data, timeout=30)
            response.raise_for_status()

            result = response.json()
            return result["choices"][0]["message"]["content"]

        except Exception as e:
            logger.error(f"调用DeepSeek API失败: {e}")
            return "抱歉，处理您的请求时出现了错误。"

    def call_doubao(self, messages: List[Dict[str, str]], temperature: float = 0.7) -> str:
        """调用字节豆包API"""
        try:
            if not self.config.DOUBAO_API_KEY:
                logger.error("豆包API密钥未配置")
                return "抱歉，豆包服务暂时不可用。"

            url = f"{self.config.DOUBAO_BASE_URL}/chat/completions"
            headers = {
                "Authorization": f"Bearer {self.config.DOUBAO_API_KEY}",
                "Content-Type": "application/json"
            }

            data = {
                "model": self.config.DOUBAO_MODEL,
                "messages": messages,
                "temperature": temperature,
                "max_tokens": 2000
            }

            response = requests.post(url, headers=headers, json=data, timeout=30)
            response.raise_for_status()

            result = response.json()
            return result["choices"][0]["message"]["content"]

        except Exception as e:
            logger.error(f"调用豆包API失败: {e}")
            return "抱歉，处理您的请求时出现了错误。"

    def call_claude(self, messages: List[Dict[str, str]], temperature: float = 0.7) -> str:
        """调用Anthropic Claude API"""
        try:
            if not ANTHROPIC_AVAILABLE:
                return "抱歉，Claude服务需要安装anthropic库。"

            if not self.config.ANTHROPIC_API_KEY:
                logger.error("Anthropic API密钥未配置")
                return "抱歉，Claude服务暂时不可用。"

            client = anthropic.Anthropic(api_key=self.config.ANTHROPIC_API_KEY)

            # 转换消息格式
            claude_messages = []
            system_message = ""

            for msg in messages:
                if msg["role"] == "system":
                    system_message = msg["content"]
                else:
                    claude_messages.append({
                        "role": msg["role"],
                        "content": msg["content"]
                    })

            response = client.messages.create(
                model=self.config.ANTHROPIC_MODEL,
                max_tokens=2000,
                temperature=temperature,
                system=system_message,
                messages=claude_messages
            )

            return response.content[0].text

        except Exception as e:
            logger.error(f"调用Claude API失败: {e}")
            return "抱歉，处理您的请求时出现了错误。"

    def call_gemini(self, messages: List[Dict[str, str]], temperature: float = 0.7) -> str:
        """调用Google Gemini API"""
        try:
            if not GOOGLE_AVAILABLE:
                return "抱歉，Gemini服务需要安装google-generativeai库。"

            if not self.config.GOOGLE_API_KEY:
                logger.error("Google API密钥未配置")
                return "抱歉，Gemini服务暂时不可用。"

            genai.configure(api_key=self.config.GOOGLE_API_KEY)
            model = genai.GenerativeModel(self.config.GOOGLE_MODEL)

            # 转换消息格式为单个提示
            prompt_parts = []
            for msg in messages:
                role = msg["role"]
                content = msg["content"]
                if role == "system":
                    prompt_parts.append(f"系统指令: {content}")
                elif role == "user":
                    prompt_parts.append(f"用户: {content}")
                elif role == "assistant":
                    prompt_parts.append(f"助手: {content}")

            prompt = "\n".join(prompt_parts)

            response = model.generate_content(
                prompt,
                generation_config=genai.types.GenerationConfig(
                    temperature=temperature,
                    max_output_tokens=2000
                )
            )

            return response.text

        except Exception as e:
            logger.error(f"调用Gemini API失败: {e}")
            return "抱歉，处理您的请求时出现了错误。"
    
    def _convert_messages_to_prompt(self, messages: List[Dict[str, str]]) -> str:
        """将消息列表转换为提示文本"""
        prompt_parts = []
        for message in messages:
            role = message["role"]
            content = message["content"]
            if role == "system":
                prompt_parts.append(f"系统: {content}")
            elif role == "user":
                prompt_parts.append(f"用户: {content}")
            elif role == "assistant":
                prompt_parts.append(f"助手: {content}")
        
        return "\n".join(prompt_parts)
    
    def generate_response(self, messages: List[Dict[str, str]],
                         model_provider: str = "openai",
                         temperature: float = 0.7,
                         model: str = None) -> str:
        """生成回复"""
        try:
            provider = model_provider.lower()

            if provider == "openai":
                return self.call_openai(messages, temperature)
            elif provider == "zhipu":
                return self.call_zhipu(messages, temperature)
            elif provider == "qwen":
                return self.call_qwen(messages, temperature)
            elif provider == "siliconflow":
                if model:
                    return self.call_siliconflow(messages, temperature, model)
                else:
                    return self.call_siliconflow(messages, temperature)
            elif provider == "siliconflow2":
                if model:
                    return self.call_siliconflow2(messages, temperature, model)
                else:
                    return self.call_siliconflow2(messages, temperature)
            elif provider == "baidu":
                return self.call_baidu(messages, temperature)
            elif provider == "moonshot":
                return self.call_moonshot(messages, temperature)
            elif provider == "deepseek":
                return self.call_deepseek(messages, temperature)
            elif provider == "doubao":
                return self.call_doubao(messages, temperature)
            elif provider == "claude":
                return self.call_claude(messages, temperature)
            elif provider == "gemini":
                return self.call_gemini(messages, temperature)
            else:
                logger.error(f"不支持的模型提供商: {model_provider}")
                return "抱歉，不支持的模型提供商。"

        except Exception as e:
            logger.error(f"生成回复失败: {e}")
            return "抱歉，生成回复时出现了错误。"
    
    def create_financial_prompt(self, user_query: str, context: Dict[str, Any]) -> List[Dict[str, str]]:
        """创建金融对话的提示"""
        # 构建系统提示
        has_images = context.get("images") and len(context.get("images", [])) > 0

        system_prompt = """你是一个专业的金融助手，具有丰富的金融知识和经验。请根据提供的上下文信息回答用户的问题。

回答要求：
1. 准确、专业、客观
2. 基于提供的上下文信息
3. 如果上下文信息不足，请明确说明
4. 使用简洁明了的语言
5. 必要时提供相关的金融术语解释"""

        if has_images:
            system_prompt += """
6. 特别注意：当提供了相关图片信息时，请充分利用这些图片内容来增强回答
7. 如果用户询问图表、数据可视化、趋势分析等内容，请重点参考图片信息
8. 在回答中可以明确提及图片中的具体内容，如"根据第X页的图表显示..."
9. 对于图片中的数据和趋势，请提供专业的分析和解读"""

        system_prompt += "\n\n请始终保持专业性和准确性。"""
        
        # 构建上下文信息
        context_text = ""
        
        # 添加知识库信息
        if context.get("knowledge"):
            context_text += "相关专业知识：\n"
            for i, item in enumerate(context["knowledge"][:3], 1):  # 只取前3条
                context_text += f"{i}. {item['content']} (来源: {item['source']})\n"
            context_text += "\n"
        
        # 添加历史对话信息
        if context.get("history"):
            context_text += "相关历史对话：\n"
            for i, item in enumerate(context["history"][:2], 1):  # 只取前2条
                context_text += f"{i}. 用户问: {item['user_query']}\n   回答: {item['assistant_response']}\n"
            context_text += "\n"

        # 添加相关图片信息
        if context.get("images"):
            context_text += "📊 相关图片信息：\n"
            for i, img in enumerate(context["images"][:3], 1):  # 只取前3张
                context_text += f"\n图片 {i}:\n"
                context_text += f"  📄 来源文档: {img.get('pdf_name', '未知')}\n"
                context_text += f"  📖 页码: 第{img.get('page_number', 0)}页\n"
                context_text += f"  🏷️ 图片类型: {img.get('image_type', '未知')}\n"
                context_text += f"  📝 详细描述: {img.get('description', '无描述')}\n"
                context_text += f"  📏 图片尺寸: {img.get('width', 0)} × {img.get('height', 0)}\n"
                context_text += f"  🎯 相关度: {(img.get('final_score', 0) * 100):.1f}%\n"

                # 如果有提取的文本，也包含进来
                if img.get('extracted_text'):
                    context_text += f"  📋 图片中的文本: {img.get('extracted_text')}\n"

            context_text += "\n💡 图片分析指导：\n"
            context_text += "- 这些图片与用户的问题高度相关，请在回答中充分利用\n"
            context_text += "- 如果用户询问数据、趋势、图表等内容，请重点参考图片信息\n"
            context_text += "- 可以在回答中明确引用图片，如\"根据第X页的图表显示...\"\n"
            context_text += "- 对图片中的数据和趋势提供专业的金融分析和解读\n"

            # 添加针对性的分析指导
            specific_guidance = self._analyze_query_image_relevance(user_query, context.get("images", []))
            context_text += specific_guidance + "\n"
        
        # 构建用户消息
        user_message = f"""上下文信息：
{context_text}

用户问题：{user_query}

请基于上述上下文信息回答用户的问题。"""
        
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_message}
        ]
        
        return messages

    def _analyze_query_image_relevance(self, query: str, images: List[Dict[str, Any]]) -> str:
        """分析查询与图片的相关性，生成针对性的指导"""
        if not images:
            return ""

        query_lower = query.lower()

        # 图表相关关键词
        chart_keywords = ["图表", "图", "数据", "趋势", "分析", "统计", "报表", "可视化", "柱状图", "折线图", "饼图"]
        # 财务相关关键词
        financial_keywords = ["收入", "利润", "营收", "增长", "市场", "财务", "业绩", "指标", "比率"]
        # 时间相关关键词
        time_keywords = ["年", "月", "季度", "期间", "历史", "变化", "对比"]

        has_chart_query = any(keyword in query_lower for keyword in chart_keywords)
        has_financial_query = any(keyword in query_lower for keyword in financial_keywords)
        has_time_query = any(keyword in query_lower for keyword in time_keywords)

        guidance = "\n🎯 针对性分析指导：\n"

        if has_chart_query:
            guidance += "- 用户询问图表相关内容，请重点分析图片中的数据表现形式\n"
            guidance += "- 描述图表类型、数据趋势、关键数据点\n"

        if has_financial_query:
            guidance += "- 用户关注财务指标，请从图片中提取相关财务数据\n"
            guidance += "- 分析财务表现、增长情况、市场地位等\n"

        if has_time_query:
            guidance += "- 用户询问时间相关变化，请关注图片中的时间序列数据\n"
            guidance += "- 分析历史趋势、周期性变化、未来预测\n"

        # 分析图片类型分布
        image_types = [img.get('image_type', '') for img in images]
        if any('图表' in img_type or '图' in img_type for img_type in image_types):
            guidance += "- 发现图表类图片，请详细解读图表内容和数据含义\n"

        return guidance
    
    def get_available_models(self) -> List[str]:
        """获取可用的模型列表"""
        available_models = []

        if self.config.OPENAI_API_KEY:
            available_models.append("openai")

        if self.config.ZHIPU_API_KEY:
            available_models.append("zhipu")

        if self.config.QWEN_API_KEY:
            available_models.append("qwen")

        if self.config.SILICONFLOW_API_KEY:
            available_models.append("siliconflow")

        if self.config.SILICONFLOW2_API_KEY:
            available_models.append("siliconflow2")

        if self.config.BAIDU_API_KEY and self.config.BAIDU_SECRET_KEY:
            available_models.append("baidu")

        if self.config.MOONSHOT_API_KEY:
            available_models.append("moonshot")

        if self.config.DEEPSEEK_API_KEY:
            available_models.append("deepseek")

        if self.config.DOUBAO_API_KEY:
            available_models.append("doubao")

        if self.config.ANTHROPIC_API_KEY and ANTHROPIC_AVAILABLE:
            available_models.append("claude")

        if self.config.GOOGLE_API_KEY and GOOGLE_AVAILABLE:
            available_models.append("gemini")

        return available_models

    def get_siliconflow_models(self) -> List[str]:
        """获取硅基流动支持的模型列表"""
        return [
            "Qwen/Qwen2.5-7B-Instruct",
            "Qwen/Qwen2.5-14B-Instruct",
            "Qwen/Qwen2.5-32B-Instruct",
            "Qwen/Qwen2.5-72B-Instruct",
            "meta-llama/Meta-Llama-3.1-8B-Instruct",
            "meta-llama/Meta-Llama-3.1-70B-Instruct",
            "meta-llama/Meta-Llama-3.1-405B-Instruct",
            "deepseek-ai/DeepSeek-V2.5",
            "01-ai/Yi-1.5-9B-Chat-16K",
            "01-ai/Yi-1.5-34B-Chat-16K",
            "google/gemma-2-9b-it",
            "google/gemma-2-27b-it",
            "mistralai/Mistral-7B-Instruct-v0.3",
            "mistralai/Mixtral-8x7B-Instruct-v0.1",
            "mistralai/Mixtral-8x22B-Instruct-v0.1"
        ]

    def get_siliconflow2_models(self) -> List[str]:
        """获取硅基流动2支持的模型列表"""
        return [
            "Qwen/Qwen2.5-72B-Instruct",
            "Qwen/Qwen2.5-32B-Instruct",
            "meta-llama/Meta-Llama-3.1-70B-Instruct",
            "meta-llama/Meta-Llama-3.1-405B-Instruct",
            "deepseek-ai/DeepSeek-V2.5",
            "deepseek-ai/DeepSeek-R1-0528-Qwen3-8B",
            "mistralai/Mixtral-8x22B-Instruct-v0.1",
            "01-ai/Yi-1.5-34B-Chat-16K",
            "google/gemma-2-27b-it"
        ]

    def get_model_info(self) -> Dict[str, Dict[str, Any]]:
        """获取所有模型的详细信息"""
        model_info = {
            "openai": {
                "name": "OpenAI GPT",
                "description": "OpenAI的GPT系列模型",
                "models": ["gpt-3.5-turbo", "gpt-4", "gpt-4-turbo"],
                "available": bool(self.config.OPENAI_API_KEY)
            },
            "zhipu": {
                "name": "智谱AI",
                "description": "智谱AI的GLM系列模型",
                "models": ["glm-4", "glm-3-turbo"],
                "default_model": self.config.ZHIPU_MODEL,
                "available": bool(self.config.ZHIPU_API_KEY)
            },
            "qwen": {
                "name": "通义千问",
                "description": "阿里云的通义千问系列模型",
                "models": ["qwen-turbo", "qwen-plus", "qwen-max"],
                "default_model": self.config.QWEN_MODEL,
                "available": bool(self.config.QWEN_API_KEY)
            },
            "siliconflow": {
                "name": "硅基流动",
                "description": "硅基流动平台的多种开源模型",
                "models": self.get_siliconflow_models(),
                "default_model": self.config.SILICONFLOW_MODEL,
                "available": bool(self.config.SILICONFLOW_API_KEY)
            },
            "siliconflow2": {
                "name": "硅基流动2",
                "description": "硅基流动平台的高性能模型配置",
                "models": self.get_siliconflow2_models(),
                "default_model": self.config.SILICONFLOW2_MODEL,
                "available": bool(self.config.SILICONFLOW2_API_KEY)
            },
            "baidu": {
                "name": "百度文心一言",
                "description": "百度的文心一言系列模型",
                "models": ["ernie-bot", "ernie-bot-turbo"],
                "default_model": self.config.BAIDU_MODEL,
                "available": bool(self.config.BAIDU_API_KEY and self.config.BAIDU_SECRET_KEY)
            },
            "moonshot": {
                "name": "Moonshot AI (Kimi)",
                "description": "月之暗面的Kimi系列模型",
                "models": ["moonshot-v1-8k", "moonshot-v1-32k", "moonshot-v1-128k"],
                "default_model": self.config.MOONSHOT_MODEL,
                "available": bool(self.config.MOONSHOT_API_KEY)
            },
            "deepseek": {
                "name": "DeepSeek",
                "description": "深度求索的DeepSeek系列模型",
                "models": ["deepseek-chat", "deepseek-coder"],
                "default_model": self.config.DEEPSEEK_MODEL,
                "available": bool(self.config.DEEPSEEK_API_KEY)
            },
            "doubao": {
                "name": "字节豆包",
                "description": "字节跳动的豆包系列模型",
                "models": ["doubao-pro", "doubao-lite"],
                "default_model": self.config.DOUBAO_MODEL,
                "available": bool(self.config.DOUBAO_API_KEY)
            },
            "claude": {
                "name": "Anthropic Claude",
                "description": "Anthropic的Claude系列模型",
                "models": ["claude-3-sonnet", "claude-3-opus", "claude-3-haiku"],
                "default_model": self.config.ANTHROPIC_MODEL,
                "available": bool(self.config.ANTHROPIC_API_KEY and ANTHROPIC_AVAILABLE)
            },
            "gemini": {
                "name": "Google Gemini",
                "description": "Google的Gemini系列模型",
                "models": ["gemini-pro", "gemini-pro-vision"],
                "default_model": self.config.GOOGLE_MODEL,
                "available": bool(self.config.GOOGLE_API_KEY and GOOGLE_AVAILABLE)
            }
        }

        return model_info
